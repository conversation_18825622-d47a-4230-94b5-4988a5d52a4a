import 'dart:ui';
import 'package:flutter/material.dart';

class CustomTheme {
  const CustomTheme();

  // TheGorgeousLogin的渐变色
  static const Color loginGradientStart = Color(0xFFfbab66);
  static const Color loginGradientEnd = Color(0xFFf7418c);
  
  // 应用原有的主色调
  static const Color primaryColor = Color(0xFF3F51B5);
  static const Color primaryLightColor = Color(0xFF7986CB);
  
  // 基础颜色
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  // 渐变定义
  static const LinearGradient primaryGradient = LinearGradient(
    colors: <Color>[loginGradientStart, loginGradientEnd],
    stops: <double>[0.0, 1.0],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // 按钮渐变
  static const LinearGradient buttonGradient = LinearGradient(
    colors: <Color>[loginGradientEnd, loginGradientStart],
    begin: FractionalOffset(0.2, 0.2),
    end: FractionalOffset(1.0, 1.0),
    stops: <double>[0.0, 1.0],
    tileMode: TileMode.clamp,
  );
  
  // 按钮阴影
  static List<BoxShadow> buttonShadow = [
    const BoxShadow(
      color: loginGradientStart,
      offset: Offset(1.0, 6.0),
      blurRadius: 20.0,
    ),
    const BoxShadow(
      color: loginGradientEnd,
      offset: Offset(1.0, 6.0),
      blurRadius: 20.0,
    ),
  ];
}
