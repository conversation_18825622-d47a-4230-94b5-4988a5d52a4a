import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../config/app_config.dart';
import '../services/user_service.dart';
import '../services/fishing_spot_service.dart';
import '../services/tile_cache_service.dart';

/// 开发者调试菜单
/// 仅在开发模式下显示，提供快速访问测试页面和开发工具的入口
class DevMenu extends StatelessWidget {
  const DevMenu({super.key});

  @override
  Widget build(BuildContext context) {
    // 如果不是开发模式，不显示菜单
    if (!AppConfig.instance.enableDeveloperTools) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      icon: const Icon(
        FontAwesomeIcons.bug,
        color: Colors.orange,
        size: 20,
      ),
      tooltip: '开发者工具',
      onSelected: (value) => _handleMenuSelection(context, value),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'supabase_test',
          child: ListTile(
            leading: Icon(FontAwesomeIcons.database, size: 16),
            title: Text('Supabase 测试'),
            subtitle: Text('测试后端功能'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'clear_cache',
          child: ListTile(
            leading: Icon(FontAwesomeIcons.trash, size: 16),
            title: Text('清除缓存'),
            subtitle: Text('清除所有本地缓存'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'reload_data',
          child: ListTile(
            leading: Icon(FontAwesomeIcons.arrowsRotate, size: 16),
            title: Text('重新加载数据'),
            subtitle: Text('刷新所有数据'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'app_info',
          child: ListTile(
            leading: Icon(FontAwesomeIcons.circleInfo, size: 16),
            title: Text('应用信息'),
            subtitle: Text('查看版本和配置'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ],
    );
  }

  /// 处理菜单选择
  void _handleMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'supabase_test':
        _openSupabaseTest(context);
        break;
      case 'clear_cache':
        _clearCache(context);
        break;
      case 'reload_data':
        _reloadData(context);
        break;
      case 'app_info':
        _showAppInfo(context);
        break;
    }
  }

  /// 打开Supabase测试页面
  void _openSupabaseTest(BuildContext context) {
    Navigator.pushNamed(context, '/dev/supabase-test');
  }

  /// 清除缓存
  void _clearCache(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除缓存'),
        content: const Text('确定要清除所有本地缓存吗？这将删除用户数据、钓点数据和地图瓦片缓存。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performClearCache(context);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 执行清除缓存操作
  Future<void> _performClearCache(BuildContext context) async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在清除缓存...'),
            ],
          ),
        ),
      );

      // 清除各种缓存
      final userService = UserService();
      final spotService = FishingSpotService();
      final tileService = TileCacheService();

      // 这里可以添加具体的缓存清除逻辑
      await Future.delayed(const Duration(seconds: 1)); // 模拟清除过程

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);
        
        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('缓存清除完成')),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);
        
        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('清除缓存失败: $e')),
        );
      }
    }
  }

  /// 重新加载数据
  void _reloadData(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重新加载数据'),
        content: const Text('确定要重新加载所有数据吗？这将从服务器获取最新数据。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performReloadData(context);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 执行重新加载数据操作
  Future<void> _performReloadData(BuildContext context) async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在重新加载数据...'),
            ],
          ),
        ),
      );

      // 重新加载数据
      final userService = UserService();
      final spotService = FishingSpotService();

      await Future.wait([
        userService.getAllUsers(),
        spotService.getAllSpots(),
      ]);

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);
        
        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('数据重新加载完成')),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (context.mounted) {
        Navigator.pop(context);
        
        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('重新加载数据失败: $e')),
        );
      }
    }
  }

  /// 显示应用信息
  void _showAppInfo(BuildContext context) {
    final config = AppConfig.instance;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('应用信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('应用版本: ${config.appVersion}'),
            Text('构建模式: ${config.buildMode}'),
            Text('平台: ${config.platformInfo}'),
            const SizedBox(height: 16),
            const Text('开发功能状态:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('开发模式: ${config.isDevelopmentMode ? '启用' : '禁用'}'),
            Text('Supabase测试: ${config.enableSupabaseTestPage ? '启用' : '禁用'}'),
            Text('网络日志: ${config.enableNetworkLogging ? '启用' : '禁用'}'),
            Text('缓存调试: ${config.enableCacheDebugging ? '启用' : '禁用'}'),
            Text('性能监控: ${config.enablePerformanceMonitoring ? '启用' : '禁用'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
