import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../main.dart';
import '../../services/user_service.dart';
import '../../services/fishing_spot_service.dart';
import '../../models/user.dart' as model_user;
import '../../models/fishing_spot.dart';
import 'dart:math';

/// Supabase功能测试页面
/// 仅在开发模式下可用，用于测试各种Supabase后端功能
class SupabaseTestPage extends StatefulWidget {
  const SupabaseTestPage({super.key});

  @override
  State<SupabaseTestPage> createState() => _SupabaseTestPageState();
}

class _SupabaseTestPageState extends State<SupabaseTestPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // 服务实例
  final UserService _userService = UserService();
  final FishingSpotService _spotService = FishingSpotService();

  // 测试数据控制器
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _passwordCheckController =
      TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _nicknameController = TextEditingController();

  // 钓点测试控制器
  final TextEditingController _spotNameController = TextEditingController();
  final TextEditingController _spotDescController = TextEditingController();
  final TextEditingController _commentController = TextEditingController();

  // 状态变量
  bool _isLoading = false;
  String _statusMessage = '';
  List<model_user.User> _users = [];
  List<FishingSpot> _spots = [];

  // 测试结果
  Map<String, dynamic>? _lastTestResult;
  String? _selectedSpotId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadInitialData();
    _initializeTestData();
  }

  /// 初始化测试数据
  void _initializeTestData() {
    // 预设一些测试数据
    _emailController.text = '<EMAIL>';
    _passwordController.text = 'test123456';
    _passwordCheckController.text = 'test123456';
    _phoneController.text = '13800138000';
    _spotNameController.text = '测试钓点';
    _spotDescController.text = '这是一个用于测试的钓点';
    _commentController.text = '这是一条测试评论';
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _passwordCheckController.dispose();
    _phoneController.dispose();
    _usernameController.dispose();
    _nicknameController.dispose();
    _spotNameController.dispose();
    _spotDescController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    setState(() {
      _statusMessage = '正在加载初始数据...';
    });

    try {
      // 加载用户列表
      final users = await _userService.getAllUsers();
      final spots = await _spotService.getAllSpots();

      setState(() {
        _users = users;
        _spots = spots;
        _statusMessage = '数据加载完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '加载数据失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase 功能测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: '认证测试'),
            Tab(text: '用户管理'),
            Tab(text: '钓点管理'),
            Tab(text: '实时订阅'),
            Tab(text: '系统信息'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAuthTestTab(),
          _buildUserManagementTab(),
          _buildSpotManagementTab(),
          _buildRealtimeTab(),
          _buildSystemInfoTab(),
        ],
      ),
    );
  }

  /// 构建认证测试标签页
  Widget _buildAuthTestTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 当前认证状态
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '当前认证状态',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text('用户ID: ${supabase.auth.currentUser?.id ?? '未登录'}'),
                  Text('邮箱: ${supabase.auth.currentUser?.email ?? '无'}'),
                  Text('登录状态: ${_userService.isLoggedIn() ? '已登录' : '未登录'}'),
                  Text('当前用户: ${_userService.currentUser?.username ?? '无'}'),
                  Text('昵称: ${_userService.currentUser?.nickname ?? '无'}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 邮箱注册测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '邮箱注册测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '测试邮箱注册功能，注册成功后自动登录',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: '邮箱',
                      border: OutlineInputBorder(),
                      hintText: '输入测试邮箱',
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _passwordController,
                    decoration: const InputDecoration(
                      labelText: '密码',
                      border: OutlineInputBorder(),
                      hintText: '输入密码',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _passwordCheckController,
                    decoration: const InputDecoration(
                      labelText: '确认密码',
                      border: OutlineInputBorder(),
                      hintText: '再次输入密码',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _testEmailRegister,
                      child:
                          _isLoading
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text('测试邮箱注册'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 登录功能测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '登录功能测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '测试用户登录功能，包括成功和失败场景',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testEmailLogin,
                          child: const Text('测试登录'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testLoginFailure,
                          child: const Text('测试登录失败'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 手机号注册测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '手机号注册测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '测试手机号注册功能，如果已存在则自动登录',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _phoneController,
                    decoration: const InputDecoration(
                      labelText: '手机号',
                      border: OutlineInputBorder(),
                      hintText: '输入测试手机号',
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _testPhoneRegister,
                      child: const Text('测试手机号注册'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 登出功能测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '登出功能测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text('测试用户登出功能', style: TextStyle(color: Colors.grey)),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _testLogout,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('测试登出'),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 测试结果显示
          if (_lastTestResult != null) _buildTestResultCard(),

          // 状态信息
          if (_statusMessage.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('状态信息', style: Theme.of(context).textTheme.titleSmall),
                    const SizedBox(height: 8),
                    Text(
                      _statusMessage,
                      style: TextStyle(
                        color:
                            _statusMessage.contains('失败') ||
                                    _statusMessage.contains('错误')
                                ? Colors.red
                                : Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建用户管理标签页
  Widget _buildUserManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '用户列表 (${_users.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _refreshUsers,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_users.isEmpty)
                    const Text('暂无用户数据')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _users.length,
                      itemBuilder: (context, index) {
                        final user = _users[index];
                        return ListTile(
                          leading: CircleAvatar(
                            child: Text(
                              user.username.substring(0, 1).toUpperCase(),
                            ),
                          ),
                          title: Text(user.username),
                          subtitle: Text('${user.nickname} - ${user.email}'),
                          trailing: Text(
                            '钓点: ${user.publishedSpots.length}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 创建测试用户
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '创建测试用户',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _usernameController,
                    decoration: const InputDecoration(
                      labelText: '用户名',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _nicknameController,
                    decoration: const InputDecoration(
                      labelText: '昵称',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _createTestUser,
                    child: const Text('创建测试用户'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建钓点管理标签页
  Widget _buildSpotManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 发布钓点测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '发布钓点测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '测试发布钓点功能（需要登录）',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _spotNameController,
                    decoration: const InputDecoration(
                      labelText: '钓点名称',
                      border: OutlineInputBorder(),
                      hintText: '输入钓点名称',
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _spotDescController,
                    decoration: const InputDecoration(
                      labelText: '钓点描述',
                      border: OutlineInputBorder(),
                      hintText: '输入钓点描述（可选）',
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _testPublishSpot,
                      child: const Text('测试发布钓点'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 读取钓点测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '读取钓点测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '测试从服务器读取钓点数据功能',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testReadSpots,
                          child: const Text('读取所有钓点'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed:
                              _isLoading
                                  ? null
                                  : () => _testReadSpotsLimited(5),
                          child: const Text('读取前5个'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 钓点评论测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '钓点评论测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '测试钓点评论功能（需要登录和选择钓点）',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  if (_spots.isNotEmpty)
                    DropdownButtonFormField<String>(
                      value: _selectedSpotId,
                      decoration: const InputDecoration(
                        labelText: '选择钓点',
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _spots.map((spot) {
                            return DropdownMenuItem<String>(
                              value: spot.id,
                              child: Text('${spot.name} (${spot.sharedBy})'),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedSpotId = value;
                        });
                      },
                    )
                  else
                    const Text('请先读取钓点数据'),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _commentController,
                    decoration: const InputDecoration(
                      labelText: '评论内容',
                      border: OutlineInputBorder(),
                      hintText: '输入评论内容',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed:
                          _isLoading || _selectedSpotId == null
                              ? null
                              : _testAddComment,
                      child: const Text('测试添加评论'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 钓点列表显示
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '钓点列表 (${_spots.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _refreshSpots,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_spots.isEmpty)
                    const Text('暂无钓点数据，请先测试读取功能')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _spots.length,
                      itemBuilder: (context, index) {
                        final spot = _spots[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: const Icon(Icons.location_on),
                            title: Text(spot.name),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('发布者: ${spot.sharedBy}'),
                                Text(
                                  '坐标: ${spot.location.latitude.toStringAsFixed(4)}, '
                                  '${spot.location.longitude.toStringAsFixed(4)}',
                                ),
                                if (spot.description.isNotEmpty)
                                  Text('描述: ${spot.description}'),
                              ],
                            ),
                            trailing: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text('照片: ${spot.photoUrls.length}'),
                                Text('评论: ${spot.comments.length}'),
                              ],
                            ),
                            onTap: () {
                              setState(() {
                                _selectedSpotId = spot.id;
                              });
                              _updateStatus('已选择钓点: ${spot.name}');
                            },
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),

          // 测试结果显示
          if (_lastTestResult != null) _buildTestResultCard(),
        ],
      ),
    );
  }

  /// 构建实时订阅标签页
  Widget _buildRealtimeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '实时订阅测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  const Text('实时订阅功能可以监听数据库变化，实现实时更新。'),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton(
                        onPressed: _testRealtimeSubscription,
                        child: const Text('测试实时订阅'),
                      ),
                      ElevatedButton(
                        onPressed: _stopRealtimeSubscription,
                        child: const Text('停止订阅'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建系统信息标签页
  Widget _buildSystemInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Supabase 连接信息',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  Text('URL: http://*************/'),
                  Text('匿名密钥: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'),
                  Text(
                    '当前会话: ${supabase.auth.currentSession?.accessToken.substring(0, 20) ?? '无'}...',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('数据统计', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 16),
                  Text('用户总数: ${_users.length}'),
                  Text('钓点总数: ${_spots.length}'),
                  Text('当前登录用户: ${_userService.currentUser?.username ?? '无'}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('测试操作', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton(
                        onPressed: _testConnection,
                        child: const Text('测试连接'),
                      ),
                      ElevatedButton(
                        onPressed: _clearCache,
                        child: const Text('清除缓存'),
                      ),
                      ElevatedButton(
                        onPressed: _loadInitialData,
                        child: const Text('重新加载数据'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建测试结果显示卡片
  Widget _buildTestResultCard() {
    if (_lastTestResult == null) return const SizedBox.shrink();

    final result = _lastTestResult!;
    final isSuccess = result['success'] == true;

    return Card(
      color: isSuccess ? Colors.green.shade50 : Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: isSuccess ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text('测试结果', style: Theme.of(context).textTheme.titleMedium),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              result['message'] ?? '无消息',
              style: TextStyle(
                color: isSuccess ? Colors.green.shade700 : Colors.red.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (result['details'] != null && result['details'].isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text(
                '详细信息:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              ...result['details'].entries.map(
                (entry) => Padding(
                  padding: const EdgeInsets.only(left: 16, bottom: 2),
                  child: Text('${entry.key}: ${entry.value}'),
                ),
              ),
            ],
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () {
                  setState(() {
                    _lastTestResult = null;
                  });
                },
                child: const Text('关闭'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // ===== 测试方法实现 =====

  /// 测试邮箱注册
  Future<void> _testEmailRegister() async {
    if (_emailController.text.isEmpty ||
        _passwordController.text.isEmpty ||
        _passwordCheckController.text.isEmpty) {
      _updateStatus('请输入邮箱、密码和确认密码');
      return;
    }

    if (_passwordController.text != _passwordCheckController.text) {
      _updateStatus('密码和确认密码不匹配');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在注册...';
    });

    try {
      debugPrint('=== 开始邮箱注册测试 ===');
      debugPrint('邮箱: ${_emailController.text}');

      final user = await _userService.registerWithEmail(
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (user != null) {
        _updateStatus('注册成功并自动登录: ${user.username}');
        debugPrint('注册成功，用户: ${user.username}, ID: ${user.id}');
        await _loadInitialData();
      } else {
        _updateStatus('注册失败');
        debugPrint('注册失败：返回用户为空');
      }
    } catch (e) {
      _updateStatus('注册失败: $e');
      debugPrint('注册失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试邮箱登录
  Future<void> _testEmailLogin() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      _updateStatus('请输入邮箱和密码');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在登录...';
    });

    try {
      final result = await _userService.testLogin(
        email: _emailController.text,
        password: _passwordController.text,
      );

      setState(() {
        _lastTestResult = result;
      });

      if (result['success']) {
        _updateStatus('登录测试成功: ${result['user']?.username}');
        await _loadInitialData();
      } else {
        _updateStatus('登录测试失败: ${result['message']}');
      }
    } catch (e) {
      _updateStatus('登录测试异常: $e');
      debugPrint('登录测试异常: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试登录失败场景
  Future<void> _testLoginFailure() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在测试登录失败场景...';
    });

    try {
      final result = await _userService.testLogin(
        email: '<EMAIL>',
        password: 'wrongpassword',
      );

      setState(() {
        _lastTestResult = result;
      });

      if (!result['success']) {
        _updateStatus('登录失败测试成功: ${result['message']}');
      } else {
        _updateStatus('登录失败测试异常：应该失败但成功了');
      }
    } catch (e) {
      _updateStatus('登录失败测试异常: $e');
      debugPrint('登录失败测试异常: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试手机号注册
  Future<void> _testPhoneRegister() async {
    if (_phoneController.text.isEmpty) {
      _updateStatus('请输入手机号');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在测试手机号注册...';
    });

    try {
      debugPrint('=== 开始手机号注册测试 ===');
      debugPrint('手机号: ${_phoneController.text}');

      final user = await _userService.registerOrLoginWithPhone(
        phoneNumber: _phoneController.text,
      );

      if (user != null) {
        _updateStatus('手机号注册/登录成功: ${user.username}');
        debugPrint('手机号注册/登录成功，用户: ${user.username}, ID: ${user.id}');
        await _loadInitialData();
      } else {
        _updateStatus('手机号注册/登录失败');
        debugPrint('手机号注册/登录失败：返回用户为空');
      }
    } catch (e) {
      _updateStatus('手机号注册/登录失败: $e');
      debugPrint('手机号注册/登录失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试登出
  Future<void> _testLogout() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在登出...';
    });

    try {
      debugPrint('=== 开始登出测试 ===');
      await _userService.logout();
      _updateStatus('登出成功');
      debugPrint('登出测试成功');
      await _loadInitialData();
    } catch (e) {
      _updateStatus('登出失败: $e');
      debugPrint('登出失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试发布钓点
  Future<void> _testPublishSpot() async {
    if (_spotNameController.text.isEmpty) {
      _updateStatus('请输入钓点名称');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在发布钓点...';
    });

    try {
      // 使用随机坐标进行测试（北京附近）
      final random = Random();
      final latitude = 39.9 + (random.nextDouble() - 0.5) * 0.1;
      final longitude = 116.4 + (random.nextDouble() - 0.5) * 0.1;

      final result = await _userService.testPublishSpot(
        spotName: _spotNameController.text,
        latitude: latitude,
        longitude: longitude,
        description:
            _spotDescController.text.isEmpty ? null : _spotDescController.text,
      );

      setState(() {
        _lastTestResult = result;
      });

      if (result['success']) {
        _updateStatus('钓点发布测试成功: ${result['spotId']}');
        await _refreshSpots();
      } else {
        _updateStatus('钓点发布测试失败: ${result['message']}');
      }
    } catch (e) {
      _updateStatus('钓点发布测试异常: $e');
      debugPrint('钓点发布测试异常: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试读取钓点
  Future<void> _testReadSpots() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在读取钓点...';
    });

    try {
      final result = await _userService.testReadSpots();

      setState(() {
        _lastTestResult = result;
      });

      if (result['success']) {
        final spots = result['spots'] as List<Map<String, dynamic>>;
        _updateStatus('钓点读取测试成功，共 ${spots.length} 个钓点');

        // 更新本地钓点列表用于显示
        await _refreshSpots();
      } else {
        _updateStatus('钓点读取测试失败: ${result['message']}');
      }
    } catch (e) {
      _updateStatus('钓点读取测试异常: $e');
      debugPrint('钓点读取测试异常: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试读取限制数量的钓点
  Future<void> _testReadSpotsLimited(int limit) async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在读取前 $limit 个钓点...';
    });

    try {
      final result = await _userService.testReadSpots(limit: limit);

      setState(() {
        _lastTestResult = result;
      });

      if (result['success']) {
        final spots = result['spots'] as List<Map<String, dynamic>>;
        _updateStatus('限制钓点读取测试成功，共 ${spots.length} 个钓点');

        // 更新本地钓点列表用于显示
        await _refreshSpots();
      } else {
        _updateStatus('限制钓点读取测试失败: ${result['message']}');
      }
    } catch (e) {
      _updateStatus('限制钓点读取测试异常: $e');
      debugPrint('限制钓点读取测试异常: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试添加评论
  Future<void> _testAddComment() async {
    if (_selectedSpotId == null) {
      _updateStatus('请先选择一个钓点');
      return;
    }

    if (_commentController.text.isEmpty) {
      _updateStatus('请输入评论内容');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在添加评论...';
    });

    try {
      final result = await _userService.testAddComment(
        spotId: _selectedSpotId!,
        content: _commentController.text,
      );

      setState(() {
        _lastTestResult = result;
      });

      if (result['success']) {
        _updateStatus('评论添加测试成功: ${result['commentId']}');
        _commentController.clear();
        await _refreshSpots();
      } else {
        _updateStatus('评论添加测试失败: ${result['message']}');
      }
    } catch (e) {
      _updateStatus('评论添加测试异常: $e');
      debugPrint('评论添加测试异常: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 创建测试用户
  Future<void> _createTestUser() async {
    if (_usernameController.text.isEmpty || _nicknameController.text.isEmpty) {
      _updateStatus('请输入用户名和昵称');
      return;
    }

    // 检查用户是否已登录
    if (!_userService.isLoggedIn()) {
      _updateStatus('创建用户失败: 请先登录');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在创建测试用户...';
    });

    try {
      final testUser = model_user.User(
        id: supabase.auth.currentUser!.id, // 使用当前登录用户的ID
        username: _usernameController.text,
        nickname: _nicknameController.text,
        email: '${_usernameController.text}@test.com',
        phoneNumber: '13800138000',
        bio: '这是一个测试用户',
        avatarUrl: '',
        following: [],
        followers: [],
        publishedSpots: [],
        favoriteSpots: [],
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      await _userService.createUserInDatabase(testUser);
      _updateStatus('测试用户创建成功: ${testUser.username}');
      await _refreshUsers();
    } catch (e) {
      _updateStatus('创建测试用户失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 刷新用户列表
  Future<void> _refreshUsers() async {
    setState(() {
      _statusMessage = '正在刷新用户列表...';
    });

    try {
      final users = await _userService.getAllUsers();
      setState(() {
        _users = users;
        _statusMessage = '用户列表刷新完成';
      });
    } catch (e) {
      _updateStatus('刷新用户列表失败: $e');
    }
  }

  /// 刷新钓点列表
  Future<void> _refreshSpots() async {
    setState(() {
      _statusMessage = '正在刷新钓点列表...';
    });

    try {
      final spots = await _spotService.getAllSpots();
      setState(() {
        _spots = spots;
        _statusMessage = '钓点列表刷新完成';
      });
    } catch (e) {
      _updateStatus('刷新钓点列表失败: $e');
    }
  }

  /// 测试实时订阅
  Future<void> _testRealtimeSubscription() async {
    setState(() {
      _statusMessage = '实时订阅功能测试 - 监听用户表变化';
    });

    try {
      // 这里可以添加实时订阅的测试代码
      _updateStatus('实时订阅功能需要进一步实现');
    } catch (e) {
      _updateStatus('实时订阅测试失败: $e');
    }
  }

  /// 停止实时订阅
  Future<void> _stopRealtimeSubscription() async {
    setState(() {
      _statusMessage = '已停止实时订阅';
    });
  }

  /// 测试连接
  Future<void> _testConnection() async {
    setState(() {
      _statusMessage = '正在测试Supabase连接...';
    });

    try {
      await supabase.from('users').select().limit(1);
      _updateStatus('连接测试成功，数据库响应正常');
    } catch (e) {
      _updateStatus('连接测试失败: $e');
    }
  }

  /// 清除缓存
  Future<void> _clearCache() async {
    setState(() {
      _statusMessage = '正在清除缓存...';
      _users.clear();
      _spots.clear();
    });

    _updateStatus('缓存已清除');
  }

  /// 更新状态信息
  void _updateStatus(String message) {
    setState(() {
      _statusMessage = message;
    });

    // 显示snackbar
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }
}
