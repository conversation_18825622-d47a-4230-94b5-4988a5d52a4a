import 'package:latlong2/latlong.dart';

/// 钓点模型类
class FishingSpot {
  /// 唯一标识符
  final String id;

  /// 钓点坐标
  final LatLng location;

  /// 钓点名称
  final String name;

  /// 分享人用户名
  final String sharedBy;

  /// 点赞数
  int likes;

  /// 不喜欢数
  int unlikes;

  /// 评论列表
  List<Comment> comments;

  /// 照片链接列表
  List<String> photoUrls;

  /// 全景照片链接
  String? panoramaPhotoUrl;

  /// 创建时间
  final DateTime createdAt;

  /// 描述信息
  String description;

  FishingSpot({
    required this.id,
    required this.location,
    required this.name,
    required this.sharedBy,
    this.likes = 0,
    this.unlikes = 0,
    this.comments = const [],
    this.photoUrls = const [],
    this.panoramaPhotoUrl,
    required this.createdAt,
    this.description = '',
  });

  /// 从JSON创建钓点对象
  factory FishingSpot.fromJson(Map<String, dynamic> json) {
    return FishingSpot(
      id: json['id'],
      location: LatLng(json['latitude'], json['longitude']),
      name: json['name'],
      sharedBy: json['sharedBy'],
      likes: json['likes'] ?? 0,
      unlikes: json['unlikes'] ?? 0,
      comments:
          (json['comments'] as List?)
              ?.map((comment) => Comment.fromJson(comment))
              .toList() ??
          [],
      photoUrls: List<String>.from(json['photoUrls'] ?? []),
      panoramaPhotoUrl: json['panoramaPhotoUrl'],
      createdAt: DateTime.parse(json['createdAt']),
      description: json['description'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'latitude': location.latitude,
      'longitude': location.longitude,
      'name': name,
      'sharedBy': sharedBy,
      'likes': likes,
      'unlikes': unlikes,
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'photoUrls': photoUrls,
      'panoramaPhotoUrl': panoramaPhotoUrl,
      'createdAt': createdAt.toIso8601String(),
      'description': description,
    };
  }
}

/// 评论模型类
class Comment {
  /// 唯一标识符
  final String id;

  /// 评论者用户名
  final String username;

  /// 评论内容
  final String content;

  /// 评论时间
  final DateTime createdAt;

  Comment({
    required this.id,
    required this.username,
    required this.content,
    required this.createdAt,
  });

  /// 从JSON创建评论对象
  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'],
      username: json['username'],
      content: json['content'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
