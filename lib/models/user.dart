import 'package:uuid/uuid.dart';

/// 用户模型类
class User {
  /// 唯一标识符
  final String id;

  /// 用户名
  final String username;

  /// 昵称
  String nickname;

  /// 邮箱
  final String email;

  /// 手机号
  String phoneNumber;

  /// 头像URL
  String avatarUrl;

  /// 个人简介
  String bio;

  /// 关注的用户ID列表
  List<String> following;

  /// 粉丝用户ID列表
  List<String> followers;

  /// 发布的钓点ID列表
  List<String> publishedSpots;

  /// 收藏的钓点ID列表
  List<String> favoriteSpots;

  /// 注册时间
  final DateTime createdAt;

  /// 最后登录时间
  DateTime lastLoginAt;

  User({
    String? id,
    required this.username,
    required this.nickname,
    required this.email,
    this.phoneNumber = '',
    this.avatarUrl = '',
    this.bio = '',
    this.following = const [],
    this.followers = const [],
    this.publishedSpots = const [],
    this.favoriteSpots = const [],
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       lastLoginAt = lastLoginAt ?? DateTime.now();

  /// 从JSON创建用户对象
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'],
      nickname: json['nickname'],
      email: json['email'] ?? '',
      phoneNumber: json['phone'] ?? json['phoneNumber'] ?? '',
      avatarUrl: json['avatar_url'] ?? json['avatarUrl'] ?? '',
      bio: json['bio'] ?? '',
      following: List<String>.from(json['following'] ?? []),
      followers: List<String>.from(json['followers'] ?? []),
      publishedSpots: List<String>.from(
        json['published_spots'] ?? json['publishedSpots'] ?? [],
      ),
      favoriteSpots: List<String>.from(
        json['favorite_spots'] ?? json['favoriteSpots'] ?? [],
      ),
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : (json['createdAt'] != null
                  ? DateTime.parse(json['createdAt'])
                  : DateTime.now()),
      lastLoginAt:
          json['last_login_at'] != null
              ? DateTime.parse(json['last_login_at'])
              : (json['lastLoginAt'] != null
                  ? DateTime.parse(json['lastLoginAt'])
                  : DateTime.now()),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      'email': email,
      'phone': phoneNumber,
      'avatar_url': avatarUrl,
      'bio': bio,
      'following': following,
      'followers': followers,
      'published_spots': publishedSpots,
      'favorite_spots': favoriteSpots,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt.toIso8601String(),
    };
  }
}

/// 消息模型类
class Message {
  /// 唯一标识符
  final String id;

  /// 发送者ID
  final String senderId;

  /// 接收者ID
  final String receiverId;

  /// 消息内容
  final String content;

  /// 发送时间
  final DateTime sentAt;

  /// 是否已读
  bool isRead;

  Message({
    String? id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    DateTime? sentAt,
    this.isRead = false,
  }) : id = id ?? const Uuid().v4(),
       sentAt = sentAt ?? DateTime.now();

  /// 从JSON创建消息对象
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      senderId: json['senderId'],
      receiverId: json['receiverId'],
      content: json['content'],
      sentAt: DateTime.parse(json['sentAt']),
      isRead: json['isRead'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'receiverId': receiverId,
      'content': content,
      'sentAt': sentAt.toIso8601String(),
      'isRead': isRead,
    };
  }
}
