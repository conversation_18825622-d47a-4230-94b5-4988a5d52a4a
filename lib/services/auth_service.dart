import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:math';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import '../models/user.dart' as model_user;
import 'user_service.dart';

/// 认证服务类，用于处理用户认证相关操作
class AuthService {
  final UserService _userService = UserService();

  /// 获取当前登录用户
  model_user.User? get currentUser => _userService.currentUser; // 修改类型

  /// 检查用户是否已登录
  bool get isLoggedIn => supabase.auth.currentUser != null;

  /// 注册新用户
  Future<model_user.User?> register({
    // 修改类型
    required String email,
    required String password,
    required String username,
    required String nickname,
    String? bio,
  }) async {
    try {
      // 检查用户名是否已存在
      final existingUsers = await supabase
          .from('users')
          .select('username')
          .eq('username', username)
          .limit(1);

      if (existingUsers.isNotEmpty) {
        throw Exception('用户名已被使用');
      }

      // 使用Supabase Auth注册用户
      final response = await supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('注册失败，请稍后重试');
      }

      // 创建用户资料
      final newUser = model_user.User(
        // 修改类型
        id: response.user!.id,
        username: username,
        nickname: nickname,
        email: email,
        bio: bio ?? '',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      // 将用户信息存储到Supabase数据库
      await _userService.createUserInDatabase(newUser);

      // 更新当前用户
      await _userService.setCurrentUser(newUser);

      return newUser;
    } on AuthException catch (e) {
      debugPrint('注册失败: ${e.message}');
      throw Exception(e.message);
    } catch (e) {
      debugPrint('注册失败: $e');
      throw Exception('注册失败: $e');
    }
  }

  /// 用户登录
  Future<model_user.User?> login({
    // 修改类型
    required String email,
    required String password,
  }) async {
    try {
      // 使用Supabase Auth登录
      final response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('登录失败，请检查邮箱和密码');
      }

      // 获取用户信息
      final userData =
          await supabase
              .from('users')
              .select()
              .eq('id', response.user!.id)
              .single();

      if (userData == null) {
        throw Exception('获取用户信息失败');
      }

      // 创建用户对象
      final user = model_user.User.fromJson(userData); // 修改类型

      // 更新最后登录时间
      await supabase
          .from('users')
          .update({'last_login_at': DateTime.now().toIso8601String()})
          .eq('id', user.id);

      // 更新当前用户
      await _userService.setCurrentUser(user);

      return user;
    } on AuthException catch (e) {
      debugPrint('登录失败: ${e.message}');
      throw Exception(e.message);
    } catch (e) {
      debugPrint('登录失败: $e');
      throw Exception('登录失败: $e');
    }
  }

  /// 使用手机号登录或注册
  Future<model_user.User?> phoneLogin({
    required String phoneNumber,
    String? verificationCode,
  }) async {
    try {
      // 检查用户是否已存在
      final existingUsers = await supabase
          .from('users')
          .select('id')
          .eq('phone', phoneNumber)
          .limit(1);

      if (existingUsers.isNotEmpty) {
        // 用户已存在，执行登录
        final userId = existingUsers[0]['id'];

        // 获取用户信息
        final userData =
            await supabase.from('users').select().eq('id', userId).single();

        // 创建用户对象
        final user = model_user.User.fromJson(userData);

        // 更新最后登录时间
        await supabase
            .from('users')
            .update({'last_login_at': DateTime.now().toIso8601String()})
            .eq('id', user.id);

        // 更新当前用户
        await _userService.setCurrentUser(user);

        return user;
      } else {
        // 用户不存在，执行注册
        // 创建Supabase Auth用户（使用随机邮箱和密码）
        final random = Random();
        final randomString = random.nextInt(999999).toString().padLeft(6, '0');
        final email = 'phone_$<EMAIL>';
        final password = 'Phone$randomString';

        final response = await supabase.auth.signUp(
          email: email,
          password: password,
        );

        if (response.user == null) {
          throw Exception('注册失败，请稍后重试');
        }

        // 生成随机用户名
        final username = 'user_${randomString}';

        // 创建用户资料
        final newUser = model_user.User(
          id: response.user!.id,
          username: username,
          nickname: '用户$randomString',
          email: email,
          phoneNumber: phoneNumber,
          bio: '',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        // 将用户信息存储到Supabase数据库
        await _userService.createUserInDatabase(newUser);

        // 更新当前用户
        await _userService.setCurrentUser(newUser);

        return newUser;
      }
    } catch (e) {
      debugPrint('手机号登录/注册失败: $e');
      throw Exception('手机号登录/注册失败: $e');
    }
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      await supabase.auth.signOut();
      await _userService.clearCurrentUser();
    } catch (e) {
      debugPrint('退出登录失败: $e');
      throw Exception('退出登录失败: $e');
    }
  }

  /// 重置密码
  Future<void> resetPassword(String email) async {
    try {
      await supabase.auth.resetPasswordForEmail(email);
    } catch (e) {
      debugPrint('重置密码失败: $e');
      throw Exception('重置密码失败: $e');
    }
  }

  /// 更新用户密码
  Future<void> updatePassword(String newPassword) async {
    try {
      await supabase.auth.updateUser(UserAttributes(password: newPassword));
    } catch (e) {
      debugPrint('更新密码失败: $e');
      throw Exception('更新密码失败: $e');
    }
  }

  /// 更新用户资料
  Future<model_user.User?> updateProfile({
    // 修改类型
    required String userId,
    String? nickname,
    String? bio,
    String? avatarUrl,
  }) async {
    try {
      // 构建更新数据
      final updateData = <String, dynamic>{};

      if (nickname != null) updateData['nickname'] = nickname;
      if (bio != null) updateData['bio'] = bio;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;

      if (updateData.isEmpty) {
        return currentUser;
      }

      // 更新用户资料
      await supabase.from('users').update(updateData).eq('id', userId);

      // 获取更新后的用户信息
      final userData =
          await supabase.from('users').select().eq('id', userId).single();

      if (userData == null) {
        throw Exception('获取用户信息失败');
      }

      // 创建用户对象
      final user = model_user.User.fromJson(userData); // 修改类型

      // 更新当前用户
      await _userService.setCurrentUser(user);

      return user;
    } catch (e) {
      debugPrint('更新用户资料失败: $e');
      throw Exception('更新用户资料失败: $e');
    }
  }

  /// 上传头像
  Future<String?> uploadAvatar(String filePath) async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('用户未登录');
      }

      // 上传头像
      final fileName = 'avatar_${user.id}.jpg';
      await supabase.storage
          .from('avatars')
          .upload(
            fileName,
            File(filePath), // 修改参数类型
            fileOptions: const FileOptions(upsert: true),
          );

      // 获取公共URL
      final avatarUrl = supabase.storage.from('avatars').getPublicUrl(fileName);

      return avatarUrl;
    } catch (e) {
      debugPrint('上传头像失败: $e');
      return null;
    }
  }
}
