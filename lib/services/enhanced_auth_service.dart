import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../main.dart';
import '../models/user.dart' as model_user;
import 'user_service.dart';
import 'dart:math';

/// 增强的认证服务类
/// 提供更好的用户体验和更强的功能
class EnhancedAuthService {
  final UserService _userService = UserService();

  // 缓存的登录凭据
  static const String _emailKey = 'cached_email';
  static const String _passwordKey = 'cached_password';
  static const String _phoneKey = 'cached_phone';
  static const String _autoLoginKey = 'auto_login_enabled';

  /// 获取当前登录用户
  model_user.User? get currentUser => _userService.currentUser;

  /// 检查用户是否已登录
  bool get isLoggedIn => supabase.auth.currentUser != null;

  /// 初始化认证服务
  Future<void> initialize() async {
    // 检查是否启用了自动登录
    final prefs = await SharedPreferences.getInstance();
    final autoLoginEnabled = prefs.getBool(_autoLoginKey) ?? false;

    if (autoLoginEnabled && !isLoggedIn) {
      await _attemptAutoLogin();
    }
  }

  /// 尝试自动登录
  Future<void> _attemptAutoLogin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedEmail = prefs.getString(_emailKey);
      final cachedPassword = prefs.getString(_passwordKey);
      final cachedPhone = prefs.getString(_phoneKey);

      // 优先尝试手机号登录
      if (cachedPhone != null && cachedPhone.isNotEmpty) {
        debugPrint('尝试使用缓存的手机号自动登录');
        await phoneLogin(phoneNumber: cachedPhone);
        return;
      }

      // 其次尝试邮箱登录
      if (cachedEmail != null &&
          cachedEmail.isNotEmpty &&
          cachedPassword != null &&
          cachedPassword.isNotEmpty) {
        debugPrint('尝试使用缓存的邮箱自动登录');
        await login(email: cachedEmail, password: cachedPassword);
        return;
      }
    } catch (e) {
      debugPrint('自动登录失败: $e');
      // 自动登录失败不影响应用启动
    }
  }

  /// 自动检测手机号
  Future<String?> detectPhoneNumber() async {
    if (kIsWeb) {
      // Web平台不支持SIM卡信息获取
      return null;
    }

    try {
      // 暂时禁用SIM卡检测功能，因为API可能不稳定
      // 后续可以根据实际需要重新实现
      debugPrint('SIM卡检测功能暂时禁用');
    } catch (e) {
      debugPrint('获取手机号失败: $e');
    }

    return null;
  }

  /// 用户登录
  Future<model_user.User?> login({
    required String email,
    required String password,
    bool rememberCredentials = true,
  }) async {
    try {
      // 使用Supabase Auth登录
      final response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('登录失败，请检查邮箱和密码');
      }

      // 获取用户信息
      final userData =
          await supabase
              .from('users')
              .select()
              .eq('id', response.user!.id)
              .single();

      // 创建用户对象
      final user = model_user.User.fromJson(userData);

      // 更新最后登录时间
      await supabase
          .from('users')
          .update({'last_login_at': DateTime.now().toIso8601String()})
          .eq('id', user.id);

      // 更新当前用户
      await _userService.setCurrentUser(user);

      // 保存登录凭据
      if (rememberCredentials) {
        await _saveCredentials(email: email, password: password);
      }

      return user;
    } on AuthException catch (e) {
      debugPrint('登录失败: ${e.message}');
      throw Exception(e.message);
    } catch (e) {
      debugPrint('登录失败: $e');
      throw Exception('登录失败: $e');
    }
  }

  /// 使用手机号登录或注册
  Future<model_user.User?> phoneLogin({
    required String phoneNumber,
    bool rememberCredentials = true,
  }) async {
    try {
      // 检查用户是否已存在
      final existingUsers = await supabase
          .from('users')
          .select('id')
          .eq('phone', phoneNumber)
          .limit(1);

      if (existingUsers.isNotEmpty) {
        // 用户已存在，执行登录
        final userId = existingUsers[0]['id'];

        // 获取用户信息
        final userData =
            await supabase.from('users').select().eq('id', userId).single();

        // 创建用户对象
        final user = model_user.User.fromJson(userData);

        // 更新最后登录时间
        await supabase
            .from('users')
            .update({'last_login_at': DateTime.now().toIso8601String()})
            .eq('id', user.id);

        // 更新当前用户
        await _userService.setCurrentUser(user);

        // 保存登录凭据
        if (rememberCredentials) {
          await _saveCredentials(phoneNumber: phoneNumber);
        }

        return user;
      } else {
        // 用户不存在，执行注册
        return await _registerWithPhone(phoneNumber, rememberCredentials);
      }
    } catch (e) {
      debugPrint('手机号登录/注册失败: $e');
      throw Exception('手机号登录/注册失败: $e');
    }
  }

  /// 使用手机号注册新用户
  Future<model_user.User?> _registerWithPhone(
    String phoneNumber,
    bool rememberCredentials,
  ) async {
    // 创建Supabase Auth用户（使用随机邮箱和密码）
    final random = Random();
    final randomString = random.nextInt(999999).toString().padLeft(6, '0');
    final email = 'phone_$<EMAIL>';
    final password = 'Phone${randomString}Temp';

    final response = await supabase.auth.signUp(
      email: email,
      password: password,
    );

    if (response.user == null) {
      throw Exception('注册失败，请稍后重试');
    }

    // 生成随机用户名
    final username = 'user_$randomString';

    // 创建用户资料
    final newUser = model_user.User(
      id: response.user!.id,
      username: username,
      nickname: '用户$randomString',
      email: email,
      phoneNumber: phoneNumber,
      bio: '',
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
    );

    // 将用户信息存储到Supabase数据库
    await _userService.createUserInDatabase(newUser);

    // 更新当前用户
    await _userService.setCurrentUser(newUser);

    // 保存登录凭据
    if (rememberCredentials) {
      await _saveCredentials(phoneNumber: phoneNumber);
    }

    return newUser;
  }

  /// 保存登录凭据
  Future<void> _saveCredentials({
    String? email,
    String? password,
    String? phoneNumber,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (email != null) {
        await prefs.setString(_emailKey, email);
      }

      if (password != null) {
        await prefs.setString(_passwordKey, password);
      }

      if (phoneNumber != null) {
        await prefs.setString(_phoneKey, phoneNumber);
      }

      // 启用自动登录
      await prefs.setBool(_autoLoginKey, true);
    } catch (e) {
      debugPrint('保存登录凭据失败: $e');
    }
  }

  /// 获取缓存的登录凭据
  Future<Map<String, String?>> getCachedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'email': prefs.getString(_emailKey),
        'password': prefs.getString(_passwordKey),
        'phone': prefs.getString(_phoneKey),
      };
    } catch (e) {
      debugPrint('获取缓存凭据失败: $e');
      return {};
    }
  }

  /// 清除登录凭据
  Future<void> clearCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_emailKey);
      await prefs.remove(_passwordKey);
      await prefs.remove(_phoneKey);
      await prefs.setBool(_autoLoginKey, false);
    } catch (e) {
      debugPrint('清除登录凭据失败: $e');
    }
  }

  /// 退出登录
  Future<void> logout({bool clearSavedCredentials = false}) async {
    try {
      await supabase.auth.signOut();
      await _userService.clearCurrentUser();

      if (clearSavedCredentials) {
        await clearCredentials();
      }
    } catch (e) {
      debugPrint('退出登录失败: $e');
      throw Exception('退出登录失败: $e');
    }
  }

  /// 检查是否需要登录
  /// 根据用户的操作类型决定是否强制登录
  bool requiresLogin(String action) {
    switch (action) {
      case 'post_spot':
      case 'add_comment':
      case 'like_spot':
      case 'profile_settings':
        return true;
      case 'view_spots':
      case 'search_spots':
      case 'view_map':
      default:
        return false;
    }
  }
}
