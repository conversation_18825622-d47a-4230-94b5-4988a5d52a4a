import 'dart:io';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:collection/collection.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;
import '../models/fishing_spot.dart';
import '../main.dart';

/// 钓点服务类，用于管理钓点数据
class FishingSpotService {
  static const String _storageBucket = 'spot_photos';

  // 钓点数据缓存
  List<FishingSpot> _spots = [];

  // 区域缓存，用于存储已加载的区域内的钓点
  final Map<String, List<FishingSpot>> _regionCache = {};

  // 缓存过期时间（毫秒）
  static const int _cacheDuration = 5 * 60 * 1000; // 5分钟

  // 缓存时间戳
  DateTime _lastAllSpotsLoadTime = DateTime.now().subtract(
    const Duration(days: 1),
  );
  final Map<String, DateTime> _regionCacheTime = {};

  // 单例模式
  static final FishingSpotService _instance = FishingSpotService._internal();

  factory FishingSpotService() {
    return _instance;
  }

  FishingSpotService._internal();

  /// 获取所有钓点
  Future<List<FishingSpot>> getAllSpots() async {
    // 检查缓存是否有效
    final now = DateTime.now();
    if (_spots.isNotEmpty &&
        now.difference(_lastAllSpotsLoadTime).inMilliseconds < _cacheDuration) {
      debugPrint('使用缓存的所有钓点数据');
      return _spots;
    }

    try {
      // 从Supabase获取钓点数据
      final response = await supabase
          .from('fishing_spots')
          .select('''
            *,
            users!inner(username),
            spot_photos(id, photo_url, is_panorama),
            comments!left(
              id,
              content,
              created_at,
              users!inner(username)
            )
          ''')
          .order('created_at', ascending: false);

      // 转换为FishingSpot对象并更新缓存
      _spots = _convertResponseToSpots(response);
      _lastAllSpotsLoadTime = now;

      // 清理区域缓存，因为全局数据已更新
      _regionCache.clear();
      _regionCacheTime.clear();

      return _spots;
    } catch (e) {
      debugPrint('获取钓点失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_spots.isNotEmpty) {
        debugPrint('使用过期的钓点缓存数据');
        return _spots;
      }

      // 如果没有缓存，尝试从本地加载
      await _loadSpots();
      return _spots;
    }
  }

  /// 根据地图范围获取钓点
  Future<List<FishingSpot>> getSpotsInBounds(
    double minLat,
    double minLng,
    double maxLat,
    double maxLng, {
    int limit = 30,
  }) async {
    // 生成区域缓存键
    final regionKey =
        '${minLat.toStringAsFixed(2)},${minLng.toStringAsFixed(2)},${maxLat.toStringAsFixed(2)},${maxLng.toStringAsFixed(2)},$limit';

    // 检查缓存是否有效
    final now = DateTime.now();
    if (_regionCache.containsKey(regionKey) &&
        _regionCacheTime.containsKey(regionKey) &&
        now.difference(_regionCacheTime[regionKey]!).inMilliseconds <
            _cacheDuration) {
      debugPrint('使用区域缓存的钓点数据: $regionKey');
      return _regionCache[regionKey]!;
    }

    try {
      // 调用Supabase函数获取范围内的钓点
      final response = await supabase
          .rpc(
            'get_spots_in_bounds',
            params: {
              'min_lat': minLat,
              'min_lng': minLng,
              'max_lat': maxLat,
              'max_lng': maxLng,
              'limit_count': limit,
            },
          )
          .select('''
            *,
            users!inner(username),
            spot_photos(id, photo_url, is_panorama),
            comments!left(
              id,
              content,
              created_at,
              users!inner(username)
            )
          ''');

      // 转换并缓存结果
      final spots = _convertResponseToSpots(response);
      _regionCache[regionKey] = spots;
      _regionCacheTime[regionKey] = now;

      return spots;
    } catch (e) {
      debugPrint('获取范围内钓点失败: $e');

      // 如果API调用失败但有缓存，返回过期的缓存
      if (_regionCache.containsKey(regionKey)) {
        debugPrint('使用过期的区域缓存数据');
        return _regionCache[regionKey]!;
      }

      // 如果没有缓存，尝试从全局缓存中筛选
      if (_spots.isNotEmpty) {
        debugPrint('从全局缓存筛选区域内的钓点');
        return _spots
            .where((spot) {
              return spot.location.latitude >= minLat &&
                  spot.location.latitude <= maxLat &&
                  spot.location.longitude >= minLng &&
                  spot.location.longitude <= maxLng;
            })
            .take(limit)
            .toList();
      }

      // 如果全局缓存也为空，返回空列表
      return [];
    }
  }

  /// 添加新钓点
  Future<FishingSpot?> addSpot(FishingSpot spot) async {
    try {
      // 检查用户是否已登录
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('用户未登录');
      }

      // 将钓点数据插入到Supabase
      final response =
          await supabase
              .from('fishing_spots')
              .insert({
                'user_id': user.id,
                'name': spot.name,
                'latitude': spot.location.latitude,
                'longitude': spot.location.longitude,
                'description': spot.description,
              })
              .select()
              .single();

      // 更新用户的已发布钓点列表
      await supabase
          .from('users')
          .update({
            'published_spots': supabase.rpc(
              'array_append',
              params: {'arr': 'published_spots', 'val': response['id']},
            ),
          })
          .eq('id', user.id);

      // 如果有照片，上传照片
      if (spot.photoUrls.isNotEmpty) {
        for (final photoPath in spot.photoUrls) {
          await addPhotoToSpot(response['id'], photoPath, false);
        }
      }

      // 如果有全景照片，上传全景照片
      if (spot.panoramaPhotoUrl != null) {
        await addPhotoToSpot(response['id'], spot.panoramaPhotoUrl!, true);
      }

      // 获取完整的钓点数据
      return await getSpotById(response['id']);
    } catch (e) {
      debugPrint('添加钓点失败: $e');
      return null;
    }
  }

  /// 更新钓点
  Future<bool> updateSpot(FishingSpot spot) async {
    try {
      // 检查用户是否已登录
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('用户未登录');
      }

      // 更新钓点数据
      await supabase
          .from('fishing_spots')
          .update({
            'name': spot.name,
            'latitude': spot.location.latitude,
            'longitude': spot.location.longitude,
            'description': spot.description,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', spot.id)
          .eq('user_id', user.id);

      return true;
    } catch (e) {
      debugPrint('更新钓点失败: $e');
      return false;
    }
  }

  /// 删除钓点
  Future<bool> deleteSpot(String id) async {
    try {
      // 检查用户是否已登录
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('用户未登录');
      }

      // 删除钓点
      await supabase
          .from('fishing_spots')
          .delete()
          .eq('id', id)
          .eq('user_id', user.id);

      // 更新用户的已发布钓点列表
      await supabase
          .from('users')
          .update({
            'published_spots': supabase.rpc(
              'array_remove',
              params: {
                'arr': 'published_spots',
                'vals': [id],
              },
            ),
          })
          .eq('id', user.id);

      return true;
    } catch (e) {
      debugPrint('删除钓点失败: $e');
      return false;
    }
  }

  /// 获取指定ID的钓点
  Future<FishingSpot?> getSpotById(String id) async {
    try {
      final response =
          await supabase
              .from('fishing_spots')
              .select('''
            *,
            users!inner(username),
            spot_photos(id, photo_url, is_panorama),
            comments!left(
              id,
              content,
              created_at,
              users!inner(username)
            )
          ''')
              .eq('id', id)
              .single();

      final spots = _convertResponseToSpots([response]);
      return spots.isNotEmpty ? spots.first : null;
    } catch (e) {
      debugPrint('获取钓点详情失败: $e');
      // 尝试从本地缓存获取
      return _spots.firstWhereOrNull((spot) => spot.id == id);
    }
  }

  /// 添加评论
  Future<bool> addComment(String spotId, String content) async {
    try {
      // 检查用户是否已登录
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('用户未登录');
      }

      // 添加评论
      await supabase.from('comments').insert({
        'spot_id': spotId,
        'user_id': user.id,
        'content': content,
      });

      return true;
    } catch (e) {
      debugPrint('添加评论失败: $e');
      return false;
    }
  }

  /// 添加点赞
  Future<bool> addLike(String spotId) async {
    try {
      // 更新钓点点赞数
      await supabase.rpc('increment_likes', params: {'spot_id': spotId});

      return true;
    } catch (e) {
      debugPrint('点赞失败: $e');
      return false;
    }
  }

  /// 添加不喜欢
  Future<bool> addUnlike(String spotId) async {
    try {
      // 更新钓点不喜欢数
      await supabase.rpc('increment_unlikes', params: {'spot_id': spotId});

      return true;
    } catch (e) {
      debugPrint('不喜欢失败: $e');
      return false;
    }
  }

  /// 上传照片并添加到钓点
  Future<String?> addPhotoToSpot(
    String spotId,
    String photoPath,
    bool isPanorama,
  ) async {
    try {
      // 检查用户是否已登录
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('用户未登录');
      }

      // 生成唯一文件名
      final fileExt = path.extension(photoPath);
      final fileName = '${const Uuid().v4()}$fileExt';
      final filePath = '$spotId/$fileName';

      // 上传文件到存储
      final file = File(photoPath);
      await supabase.storage.from(_storageBucket).upload(filePath, file);

      // 获取公共URL
      final photoUrl = supabase.storage
          .from(_storageBucket)
          .getPublicUrl(filePath);

      // 添加照片记录到数据库
      await supabase.from('spot_photos').insert({
        'spot_id': spotId,
        'photo_url': photoUrl,
        'is_panorama': isPanorama,
      });

      return photoUrl;
    } catch (e) {
      debugPrint('上传照片失败: $e');
      return null;
    }
  }

  /// 从Supabase响应转换为FishingSpot对象列表
  List<FishingSpot> _convertResponseToSpots(List<dynamic> response) {
    return response.map((item) {
      // 获取用户名
      final username = item['users']['username'] as String;

      // 处理照片
      final photos = (item['spot_photos'] as List<dynamic>?) ?? [];
      final photoUrls = <String>[];
      String? panoramaPhotoUrl;

      for (final photo in photos) {
        if (photo['is_panorama'] == true) {
          panoramaPhotoUrl = photo['photo_url'];
        } else {
          photoUrls.add(photo['photo_url']);
        }
      }

      // 处理评论
      final commentsData = (item['comments'] as List<dynamic>?) ?? [];
      final comments =
          commentsData.map((comment) {
            return Comment(
              id: comment['id'],
              username: comment['users']['username'],
              content: comment['content'],
              createdAt: DateTime.parse(comment['created_at']),
            );
          }).toList();

      // 创建钓点对象
      return FishingSpot(
        id: item['id'],
        location: LatLng(item['latitude'], item['longitude']),
        name: item['name'],
        sharedBy: username,
        likes: item['likes'] ?? 0,
        unlikes: item['unlikes'] ?? 0,
        comments: comments,
        photoUrls: photoUrls,
        panoramaPhotoUrl: panoramaPhotoUrl,
        createdAt: DateTime.parse(item['created_at']),
        description: item['description'] ?? '',
      );
    }).toList();
  }

  /// 从本地存储加载钓点数据（备用方法）
  Future<void> _loadSpots() async {
    // 如果无法从Supabase加载数据，使用示例数据
    await generateSampleData();
  }

  /// 生成示例数据
  Future<void> generateSampleData() async {
    if (_spots.isEmpty) {
      _spots = [
        FishingSpot(
          id: '1',
          location: LatLng(39.9087, 116.3975),
          name: '北京天安门广场',
          sharedBy: '钓鱼达人',
          likes: 15,
          unlikes: 2,
          comments: [
            Comment(
              id: 'c1',
              username: '钓友小王',
              content: '这里环境不错，适合休闲钓鱼',
              createdAt: DateTime.now().subtract(const Duration(days: 5)),
            ),
            Comment(
              id: 'c2',
              username: '垂钓者',
              content: '水质清澈，鱼种丰富，推荐！',
              createdAt: DateTime.now().subtract(const Duration(days: 3)),
            ),
          ],
          photoUrls: ['https://example.com/photo1.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          description: '这是一个示例钓点，环境优美，鱼种丰富。周边设施完善，交通便利，适合全家出行。',
        ),
        FishingSpot(
          id: '2',
          location: LatLng(39.9837, 116.2973),
          name: '颐和园昆明湖',
          sharedBy: '钓友小王',
          likes: 28,
          unlikes: 1,
          comments: [
            Comment(
              id: 'c3',
              username: '钓鱼达人',
              content: '风景优美，适合休闲垂钓',
              createdAt: DateTime.now().subtract(const Duration(days: 10)),
            ),
          ],
          photoUrls: ['https://example.com/photo2.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          description: '昆明湖水域广阔，环境优美，是休闲垂钓的好去处。湖中鱼种较多，主要有鲤鱼、鲫鱼等。',
        ),
        FishingSpot(
          id: '3',
          location: LatLng(31.2304, 121.4737),
          name: '上海外滩黄浦江',
          sharedBy: '垂钓者',
          likes: 42,
          unlikes: 5,
          comments: [
            Comment(
              id: 'c4',
              username: '钓鱼达人',
              content: '夜景很美，但人比较多',
              createdAt: DateTime.now().subtract(const Duration(days: 7)),
            ),
            Comment(
              id: 'c5',
              username: '钓友小王',
              content: '鱼获不错，主要是鲫鱼和鲢鱼',
              createdAt: DateTime.now().subtract(const Duration(days: 6)),
            ),
          ],
          photoUrls: ['https://example.com/photo3.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          description: '黄浦江边垂钓，可以欣赏上海美丽的城市夜景。江中鱼种丰富，适合有经验的钓友。',
        ),
        FishingSpot(
          id: '4',
          location: LatLng(22.5431, 114.0579),
          name: '深圳湾公园',
          sharedBy: '钓鱼达人',
          likes: 36,
          unlikes: 3,
          comments: [],
          photoUrls: ['https://example.com/photo4.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          description: '深圳湾公园环境优美，空气清新，是休闲垂钓的好去处。这里靠近海边，可以钓到一些海鱼。',
        ),
        FishingSpot(
          id: '5',
          location: LatLng(30.2743, 120.1551),
          name: '杭州西湖',
          sharedBy: '垂钓者',
          likes: 53,
          unlikes: 2,
          comments: [
            Comment(
              id: 'c6',
              username: '钓友小王',
              content: '风景如画，适合休闲钓鱼',
              createdAt: DateTime.now().subtract(const Duration(days: 4)),
            ),
          ],
          photoUrls: ['https://example.com/photo5.jpg'],
          createdAt: DateTime.now().subtract(const Duration(days: 8)),
          description: '西湖景色优美，环境宜人，是休闲垂钓的绝佳场所。湖中鱼种丰富，主要有鲤鱼、鲫鱼、草鱼等。',
        ),
      ];
      _spots = _spots;
    }
  }
}
