# Supabase 功能测试指南

## 概述

本文档描述了如何使用优化后的 `supabase_test_page.dart` 和 `user_service.dart` 进行 Supabase 后端功能测试。

## 测试功能列表

### 1. 邮箱注册测试
- **功能**: 测试用户通过邮箱注册功能
- **特点**: 注册成功后自动使用该账号登录
- **调试信息**: 通过 debugPrint 在后台打印详细的操作信息和结果
- **使用方法**: 
  1. 在邮箱注册测试卡片中输入邮箱和密码
  2. 点击"测试邮箱注册"按钮
  3. 查看控制台输出的详细日志

### 2. 登录功能测试
- **功能**: 测试用户登录功能，包括成功和失败场景
- **测试场景**:
  - 成功登录：使用正确的邮箱和密码
  - 失败登录：使用不存在的邮箱或错误密码
- **使用方法**:
  1. 输入邮箱和密码
  2. 点击"测试登录"或"测试登录失败"按钮
  3. 查看测试结果卡片显示的详细信息

### 3. 手机号注册测试
- **功能**: 测试用户通过手机号注册功能
- **特点**: 基于之前的一键手机号登录需求，如果用户已存在则自动登录
- **使用方法**:
  1. 输入手机号
  2. 点击"测试手机号注册"按钮
  3. 查看注册或登录结果

### 4. 登出功能测试
- **功能**: 测试用户登出功能
- **特点**: 完全清除本地和远程登录状态
- **使用方法**: 点击"测试登出"按钮

### 5. 登录后业务功能测试

#### 5.1 发布钓点功能测试
- **功能**: 测试发布钓点功能
- **前提**: 需要用户已登录
- **使用方法**:
  1. 确保已登录
  2. 输入钓点名称和描述
  3. 点击"测试发布钓点"按钮
  4. 系统会自动生成随机坐标进行测试

#### 5.2 从服务器读取钓点数据功能测试
- **功能**: 测试从服务器读取钓点数据
- **测试选项**:
  - 读取所有钓点
  - 读取前5个钓点
- **使用方法**: 点击相应的读取按钮

#### 5.3 钓点评论功能测试
- **功能**: 测试钓点评论功能
- **前提**: 需要用户已登录且已选择钓点
- **使用方法**:
  1. 先读取钓点数据
  2. 从下拉列表中选择钓点
  3. 输入评论内容
  4. 点击"测试添加评论"按钮

## 技术特点

### 1. 通过 user_service.dart 实现
- 所有与用户操作相关的功能都通过 `user_service.dart` 实现
- 不在测试页面中直接调用 Supabase API
- 保持代码结构清晰，职责分离明确

### 2. 现代化 Flutter 开发最佳实践
- 使用 ValueNotifier 进行状态管理
- 完善的错误处理机制
- 异步操作的正确处理

### 3. 完善的错误处理
- 网络错误处理
- 认证失败处理
- 数据验证错误处理
- 用户友好的错误提示

### 4. 清晰的 UI 界面
- 每个测试功能都有对应的按钮和状态显示
- 测试结果通过卡片形式展示
- 加载状态指示器
- 颜色编码的成功/失败状态

### 5. 详细的调试输出
- 所有测试操作的结果都通过 debugPrint 输出到控制台
- 包含操作时间、用户信息、错误详情等
- 便于开发调试和问题排查

## 开发/生产环境支持

- 支持开发/生产环境切换
- 保持与现有登录状态管理的兼容性
- 环境配置可在系统信息标签页查看

## 使用建议

1. **测试顺序**: 建议按照注册 -> 登录 -> 业务功能的顺序进行测试
2. **查看日志**: 测试时请关注控制台输出的详细日志
3. **测试数据**: 页面已预设测试数据，可直接使用或修改
4. **错误处理**: 遇到错误时查看测试结果卡片的详细信息
5. **状态检查**: 在当前认证状态卡片中查看登录状态

## 注意事项

- 测试页面仅在开发模式下可用
- 发布钓点和评论功能需要用户已登录
- 手机号注册会创建临时邮箱账号
- 所有测试数据都会保存到实际数据库中
