-- =====================================================
-- Flutter钓鱼应用数据库架构 - 完整迁移文件
-- 版本: 2.0
-- 创建时间: 2025-06-24
-- =====================================================

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- 1. 用户表 (users)
-- =====================================================
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username TEXT NOT NULL UNIQUE,
  nickname TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT DEFAULT '',
  avatar_url TEXT DEFAULT '',
  bio TEXT DEFAULT '',
  points INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_login_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建users表的更新时间戳触发器
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全(RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "允许认证用户插入自己的用户记录"
ON users FOR INSERT
WITH CHECK (auth.uid() = id);

CREATE POLICY "允许用户读取所有用户信息"
ON users FOR SELECT
USING (true);

CREATE POLICY "允许用户更新自己的信息"
ON users FOR UPDATE
USING (auth.uid() = id);

-- =====================================================
-- 2. 钓点表 (fishing_spots)
-- =====================================================
CREATE TABLE fishing_spots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  latitude DOUBLE PRECISION NOT NULL,
  longitude DOUBLE PRECISION NOT NULL,
  description TEXT DEFAULT '',
  likes_count INTEGER DEFAULT 0,
  unlikes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  photos_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建fishing_spots表的更新时间戳触发器
CREATE TRIGGER update_fishing_spots_updated_at
    BEFORE UPDATE ON fishing_spots
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 创建地理位置索引（用于空间查询优化）
CREATE INDEX idx_fishing_spots_location ON fishing_spots (latitude, longitude);
CREATE INDEX idx_fishing_spots_user_id ON fishing_spots (user_id);
CREATE INDEX idx_fishing_spots_created_at ON fishing_spots (created_at DESC);

-- 注意: fishing_spots表不启用RLS，允许未登录用户读取钓点数据

-- =====================================================
-- 3. 钓点照片表 (spot_photos)
-- =====================================================
CREATE TABLE spot_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  spot_id UUID NOT NULL REFERENCES fishing_spots(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  is_panorama BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_spot_photos_spot_id ON spot_photos (spot_id);
CREATE INDEX idx_spot_photos_user_id ON spot_photos (user_id);

-- =====================================================
-- 4. 评论表 (comments)
-- =====================================================
CREATE TABLE comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  spot_id UUID NOT NULL REFERENCES fishing_spots(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_comments_spot_id ON comments (spot_id);
CREATE INDEX idx_comments_user_id ON comments (user_id);
CREATE INDEX idx_comments_created_at ON comments (created_at DESC);

-- =====================================================
-- 5. 钓点点赞表 (spot_likes)
-- =====================================================
CREATE TABLE spot_likes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  spot_id UUID NOT NULL REFERENCES fishing_spots(id) ON DELETE CASCADE,
  is_like BOOLEAN NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, spot_id)
);

-- 启用行级安全(RLS)
ALTER TABLE spot_likes ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "用户只能查看所有点赞记录"
ON spot_likes FOR SELECT
USING (true);

CREATE POLICY "用户只能插入自己的点赞记录"
ON spot_likes FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户只能更新自己的点赞记录"
ON spot_likes FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "用户只能删除自己的点赞记录"
ON spot_likes FOR DELETE
USING (auth.uid() = user_id);

-- 创建索引
CREATE INDEX idx_spot_likes_user_id ON spot_likes (user_id);
CREATE INDEX idx_spot_likes_spot_id ON spot_likes (spot_id);

-- =====================================================
-- 6. 钓点收藏表 (spot_favorites)
-- =====================================================
CREATE TABLE spot_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  spot_id UUID NOT NULL REFERENCES fishing_spots(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, spot_id)
);

-- 启用行级安全(RLS)
ALTER TABLE spot_favorites ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "用户只能查看自己的收藏记录"
ON spot_favorites FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "用户只能插入自己的收藏记录"
ON spot_favorites FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户只能删除自己的收藏记录"
ON spot_favorites FOR DELETE
USING (auth.uid() = user_id);

-- 创建索引
CREATE INDEX idx_spot_favorites_user_id ON spot_favorites (user_id);
CREATE INDEX idx_spot_favorites_spot_id ON spot_favorites (spot_id);

-- =====================================================
-- 7. 用户关注表 (user_follows)
-- =====================================================
CREATE TABLE user_follows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  follower_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  following_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(follower_id, following_id),
  CHECK (follower_id != following_id)
);

-- 启用行级安全(RLS)
ALTER TABLE user_follows ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "用户可以查看所有关注关系"
ON user_follows FOR SELECT
USING (true);

CREATE POLICY "用户只能创建自己的关注记录"
ON user_follows FOR INSERT
WITH CHECK (auth.uid() = follower_id);

CREATE POLICY "用户只能删除自己的关注记录"
ON user_follows FOR DELETE
USING (auth.uid() = follower_id);

-- 创建索引
CREATE INDEX idx_user_follows_follower_id ON user_follows (follower_id);
CREATE INDEX idx_user_follows_following_id ON user_follows (following_id);

-- =====================================================
-- 8. 消息表 (messages)
-- =====================================================
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  receiver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 启用行级安全(RLS)
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "用户只能查看自己相关的消息"
ON messages FOR SELECT
USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

CREATE POLICY "用户只能发送消息"
ON messages FOR INSERT
WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "用户只能更新接收到的消息状态"
ON messages FOR UPDATE
USING (auth.uid() = receiver_id);

-- 创建索引
CREATE INDEX idx_messages_sender_id ON messages (sender_id);
CREATE INDEX idx_messages_receiver_id ON messages (receiver_id);
CREATE INDEX idx_messages_created_at ON messages (created_at DESC);

-- =====================================================
-- 9. 数据库函数 (Database Functions)
-- =====================================================

-- 获取指定范围内的钓点
CREATE OR REPLACE FUNCTION get_spots_in_bounds(
  min_lat DOUBLE PRECISION,
  min_lng DOUBLE PRECISION,
  max_lat DOUBLE PRECISION,
  max_lng DOUBLE PRECISION,
  limit_count INTEGER DEFAULT 100
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  name TEXT,
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  description TEXT,
  likes_count INTEGER,
  unlikes_count INTEGER,
  comments_count INTEGER,
  photos_count INTEGER,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    fs.id,
    fs.user_id,
    fs.name,
    fs.latitude,
    fs.longitude,
    fs.description,
    fs.likes_count,
    fs.unlikes_count,
    fs.comments_count,
    fs.photos_count,
    fs.created_at,
    fs.updated_at
  FROM fishing_spots fs
  WHERE fs.latitude BETWEEN min_lat AND max_lat
    AND fs.longitude BETWEEN min_lng AND max_lng
  ORDER BY fs.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 更新钓点统计计数器的函数
CREATE OR REPLACE FUNCTION update_spot_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- 更新点赞统计
  IF TG_TABLE_NAME = 'spot_likes' THEN
    IF TG_OP = 'INSERT' THEN
      IF NEW.is_like THEN
        UPDATE fishing_spots SET likes_count = likes_count + 1 WHERE id = NEW.spot_id;
      ELSE
        UPDATE fishing_spots SET unlikes_count = unlikes_count + 1 WHERE id = NEW.spot_id;
      END IF;
      RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
      -- 处理点赞状态变更
      IF OLD.is_like != NEW.is_like THEN
        IF NEW.is_like THEN
          UPDATE fishing_spots SET
            likes_count = likes_count + 1,
            unlikes_count = unlikes_count - 1
          WHERE id = NEW.spot_id;
        ELSE
          UPDATE fishing_spots SET
            likes_count = likes_count - 1,
            unlikes_count = unlikes_count + 1
          WHERE id = NEW.spot_id;
        END IF;
      END IF;
      RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
      IF OLD.is_like THEN
        UPDATE fishing_spots SET likes_count = likes_count - 1 WHERE id = OLD.spot_id;
      ELSE
        UPDATE fishing_spots SET unlikes_count = unlikes_count - 1 WHERE id = OLD.spot_id;
      END IF;
      RETURN OLD;
    END IF;
  END IF;

  -- 更新评论统计
  IF TG_TABLE_NAME = 'comments' THEN
    IF TG_OP = 'INSERT' THEN
      UPDATE fishing_spots SET comments_count = comments_count + 1 WHERE id = NEW.spot_id;
      RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
      UPDATE fishing_spots SET comments_count = comments_count - 1 WHERE id = OLD.spot_id;
      RETURN OLD;
    END IF;
  END IF;

  -- 更新照片统计
  IF TG_TABLE_NAME = 'spot_photos' THEN
    IF TG_OP = 'INSERT' THEN
      UPDATE fishing_spots SET photos_count = photos_count + 1 WHERE id = NEW.spot_id;
      RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
      UPDATE fishing_spots SET photos_count = photos_count - 1 WHERE id = OLD.spot_id;
      RETURN OLD;
    END IF;
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;
-- =====================================================
-- 10. 触发器 (Triggers)
-- =====================================================

-- 为spot_likes表创建统计更新触发器
CREATE TRIGGER trigger_update_likes_stats
    AFTER INSERT OR UPDATE OR DELETE ON spot_likes
    FOR EACH ROW
    EXECUTE FUNCTION update_spot_stats();

-- 为comments表创建统计更新触发器
CREATE TRIGGER trigger_update_comments_stats
    AFTER INSERT OR DELETE ON comments
    FOR EACH ROW
    EXECUTE FUNCTION update_spot_stats();

-- 为spot_photos表创建统计更新触发器
CREATE TRIGGER trigger_update_photos_stats
    AFTER INSERT OR DELETE ON spot_photos
    FOR EACH ROW
    EXECUTE FUNCTION update_spot_stats();

-- =====================================================
-- 11. 辅助函数 (Helper Functions)
-- =====================================================

-- 计算两点间距离的函数（单位：公里）
CREATE OR REPLACE FUNCTION calculate_distance(
  lat1 DOUBLE PRECISION,
  lng1 DOUBLE PRECISION,
  lat2 DOUBLE PRECISION,
  lng2 DOUBLE PRECISION
)
RETURNS DOUBLE PRECISION AS $$
DECLARE
  earth_radius DOUBLE PRECISION := 6371; -- 地球半径（公里）
  dlat DOUBLE PRECISION;
  dlng DOUBLE PRECISION;
  a DOUBLE PRECISION;
  c DOUBLE PRECISION;
BEGIN
  dlat := radians(lat2 - lat1);
  dlng := radians(lng2 - lng1);
  a := sin(dlat/2) * sin(dlat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlng/2) * sin(dlng/2);
  c := 2 * atan2(sqrt(a), sqrt(1-a));
  RETURN earth_radius * c;
END;
$$ LANGUAGE plpgsql;

-- 获取用户附近的钓点
CREATE OR REPLACE FUNCTION get_nearby_spots(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 10,
  limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  name TEXT,
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  description TEXT,
  likes_count INTEGER,
  unlikes_count INTEGER,
  comments_count INTEGER,
  photos_count INTEGER,
  distance_km DOUBLE PRECISION,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    fs.id,
    fs.user_id,
    fs.name,
    fs.latitude,
    fs.longitude,
    fs.description,
    fs.likes_count,
    fs.unlikes_count,
    fs.comments_count,
    fs.photos_count,
    calculate_distance(user_lat, user_lng, fs.latitude, fs.longitude) as distance_km,
    fs.created_at,
    fs.updated_at
  FROM fishing_spots fs
  WHERE calculate_distance(user_lat, user_lng, fs.latitude, fs.longitude) <= radius_km
  ORDER BY distance_km ASC, fs.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 获取用户统计信息
CREATE OR REPLACE FUNCTION get_user_stats(user_uuid UUID)
RETURNS TABLE (
  published_spots_count BIGINT,
  favorite_spots_count BIGINT,
  followers_count BIGINT,
  following_count BIGINT,
  total_likes_received BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM fishing_spots WHERE user_id = user_uuid) as published_spots_count,
    (SELECT COUNT(*) FROM spot_favorites WHERE user_id = user_uuid) as favorite_spots_count,
    (SELECT COUNT(*) FROM user_follows WHERE following_id = user_uuid) as followers_count,
    (SELECT COUNT(*) FROM user_follows WHERE follower_id = user_uuid) as following_count,
    (SELECT COALESCE(SUM(fs.likes_count), 0) FROM fishing_spots fs WHERE fs.user_id = user_uuid) as total_likes_received;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 12. 存储桶策略 (Storage Policies)
-- =====================================================

-- 创建照片存储桶（如果不存在）
INSERT INTO storage.buckets (id, name, public)
VALUES ('fishing-photos', 'fishing-photos', true)
ON CONFLICT (id) DO NOTHING;

-- 存储桶RLS策略
CREATE POLICY "允许认证用户上传照片"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'fishing-photos'
  AND auth.role() = 'authenticated'
);

CREATE POLICY "允许所有人查看照片"
ON storage.objects FOR SELECT
USING (bucket_id = 'fishing-photos');

CREATE POLICY "允许用户删除自己上传的照片"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'fishing-photos'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- =====================================================
-- 13. 初始化数据 (Initial Data)
-- =====================================================

-- 创建系统管理员用户（可选）
-- INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
-- VALUES (
--   gen_random_uuid(),
--   '<EMAIL>',
--   crypt('admin123', gen_salt('bf')),
--   NOW(),
--   NOW(),
--   NOW()
-- );

-- =====================================================
-- 14. 性能优化建议
-- =====================================================

-- 定期清理过期数据的函数（可选）
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
  -- 清理30天前的已读消息
  DELETE FROM messages
  WHERE is_read = true
    AND created_at < NOW() - INTERVAL '30 days';

  -- 可以添加其他清理逻辑
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 迁移完成
-- =====================================================

-- 输出迁移完成信息
DO $$
BEGIN
  RAISE NOTICE '==============================================';
  RAISE NOTICE 'Flutter钓鱼应用数据库迁移完成！';
  RAISE NOTICE '版本: 2.0';
  RAISE NOTICE '创建时间: %', NOW();
  RAISE NOTICE '==============================================';
  RAISE NOTICE '已创建的表:';
  RAISE NOTICE '- users (用户表)';
  RAISE NOTICE '- fishing_spots (钓点表)';
  RAISE NOTICE '- spot_photos (钓点照片表)';
  RAISE NOTICE '- comments (评论表)';
  RAISE NOTICE '- spot_likes (钓点点赞表)';
  RAISE NOTICE '- spot_favorites (钓点收藏表)';
  RAISE NOTICE '- user_follows (用户关注表)';
  RAISE NOTICE '- messages (消息表)';
  RAISE NOTICE '==============================================';
  RAISE NOTICE '已创建的功能:';
  RAISE NOTICE '- RLS行级安全策略';
  RAISE NOTICE '- 地理位置索引和查询';
  RAISE NOTICE '- 自动统计计数器';
  RAISE NOTICE '- 存储桶和照片管理';
  RAISE NOTICE '- 辅助查询函数';
  RAISE NOTICE '==============================================';
END $$;
