# Supabase 测试页面使用指南

## 概述

本项目已成功集成了一个专门用于测试Supabase后端功能的开发页面，该页面仅在开发模式下可用，提供了全面的后端功能测试界面。

## 功能特性

### 1. 开发环境控制
- **自动环境检测**: 基于Flutter的`kDebugMode`自动判断是否为开发模式
- **环境变量支持**: 可通过`FLUTTER_DEV_MODE`环境变量手动控制
- **生产环境隐藏**: 在release模式下自动隐藏所有开发功能

### 2. Supabase功能测试
- **用户认证测试**: 邮箱登录、手机号登录、登出功能
- **数据库CRUD操作**: 读取、写入、更新、删除测试
- **用户管理**: 用户列表查看、创建测试用户
- **钓点管理**: 钓点数据查看、测试钓点创建
- **实时订阅**: 实时数据监听功能测试
- **系统信息**: 连接状态、配置信息查看

### 3. 开发者工具菜单
- **快速访问**: 通过主页面右上角的虫子图标访问
- **缓存管理**: 清除本地缓存、重新加载数据
- **应用信息**: 查看版本、构建模式、平台信息
- **一键测试**: 直接跳转到Supabase测试页面

## 使用方法

### 访问测试页面

#### 方法1: 通过开发者菜单（推荐）
1. 在应用主页面，点击右上角的虫子图标（🐛）
2. 选择"Supabase 测试"选项
3. 自动跳转到测试页面

#### 方法2: 直接路由访问
```dart
Navigator.pushNamed(context, '/dev/supabase-test');
```

### 测试功能使用

#### 认证测试
1. **邮箱登录测试**:
   - 输入邮箱和密码
   - 点击"登录"按钮
   - 查看登录状态和结果

2. **手机号登录测试**:
   - 输入手机号
   - 点击"手机号登录/注册"
   - 系统会自动判断是登录还是注册

#### 数据库操作测试
1. **读取测试**: 测试从数据库读取数据
2. **写入测试**: 创建测试钓点（需要先登录）
3. **更新测试**: 更新当前用户信息（需要先登录）
4. **删除测试**: 安全考虑，仅显示提示信息

#### 用户管理
- 查看所有用户列表
- 刷新用户数据
- 创建测试用户（功能待完善）

#### 钓点管理
- 查看所有钓点列表
- 刷新钓点数据
- 查看钓点详细信息

## 配置说明

### 环境配置类 (`lib/config/app_config.dart`)

```dart
// 检查是否为开发模式
bool isDevelopmentMode = AppConfig.instance.isDevelopmentMode;

// 检查是否启用Supabase测试页面
bool enableTest = AppConfig.instance.enableSupabaseTestPage;
```

### 主要配置项
- `isDevelopmentMode`: 是否为开发模式
- `enableSupabaseTestPage`: 是否启用Supabase测试页面
- `enableDeveloperTools`: 是否启用开发者工具
- `enableNetworkLogging`: 是否启用网络日志
- `enableCacheDebugging`: 是否启用缓存调试

## 文件结构

```
lib/
├── config/
│   └── app_config.dart          # 应用配置管理
├── pages/dev/
│   └── supabase_test_page.dart  # Supabase测试页面
├── widgets/
│   └── dev_menu.dart            # 开发者菜单组件
└── main.dart                    # 路由配置
```

## 安全特性

1. **生产环境保护**: 在release模式下自动禁用所有开发功能
2. **路由保护**: 开发路由仅在开发模式下注册
3. **UI隐藏**: 开发菜单在生产环境中完全隐藏
4. **权限检查**: 数据库操作需要用户登录验证

## 扩展开发

### 添加新的测试功能
1. 在`SupabaseTestPage`中添加新的标签页
2. 实现对应的测试方法
3. 更新UI界面

### 添加新的开发工具
1. 在`DevMenu`中添加新的菜单项
2. 实现对应的功能方法
3. 更新菜单构建逻辑

## 注意事项

1. **仅开发使用**: 此功能仅用于开发和测试，不应在生产环境中使用
2. **数据安全**: 测试时请注意不要误操作重要数据
3. **网络依赖**: 测试功能需要网络连接到Supabase服务器
4. **权限要求**: 某些功能需要用户登录后才能使用

## 故障排除

### 常见问题
1. **测试页面无法访问**: 检查是否在开发模式下运行
2. **功能测试失败**: 检查网络连接和Supabase配置
3. **开发菜单不显示**: 确认`AppConfig.instance.enableDeveloperTools`为true

### 调试建议
1. 查看控制台日志输出
2. 检查网络请求状态
3. 验证用户登录状态
4. 确认数据库连接正常

## 更新日志

- **v1.0.0**: 初始版本，包含基础的Supabase功能测试
- 支持用户认证、数据库CRUD、用户管理、钓点管理等功能
- 集成开发者工具菜单和环境配置管理
